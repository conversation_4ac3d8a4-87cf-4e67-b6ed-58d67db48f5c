from Components.YoutubeDownloader import download_youtube_video
from Components.Edit import extractAudio, crop_video
from Components.Transcription import transcribeAudio
from Components.LanguageTasks import GetHighlight
from Components.FaceCrop import crop_to_vertical, combine_videos

url = input("Enter YouTube video URL: ")

# Ask user for subtitle style preference
print("\nChoose subtitle style:")
print("1. Modern (white text with black outline)")
print("2. <PERSON><PERSON><PERSON><PERSON> (yellow text, uppercase)")
print("3. Instagram (white text with gray outline)")
style_choice = input("Enter choice (1-3) or press Enter for default: ").strip()

style_map = {
    '1': 'modern',
    '2': 'tiktok',
    '3': 'instagram'
}
subtitle_style = style_map.get(style_choice, 'modern')
print(f"Selected subtitle style: {subtitle_style}")

Vid= download_youtube_video(url)
if Vid:
    Vid = Vid.replace(".webm", ".mp4")
    print(f"Downloaded video and audio files successfully! at {Vid}")

    Audio = extractAudio(Vid)
    if Audio:

        transcriptions = transcribeAudio(Audio)
        if len(transcriptions) > 0:
            TransText = ""

            for text, start, end in transcriptions:
                TransText += (f"{start} - {end}: {text}")

            start , stop = GetHighlight(TransText)
            if start != 0 and stop != 0:
                print(f"Start: {start} , End: {stop}")

                Output = "Out.mp4"

                crop_video(Vid, Output, start, stop)
                croped = "croped.mp4"

                crop_to_vertical("Out.mp4", croped)

                # Filter transcriptions to match the cropped video timeframe
                cropped_transcriptions = []
                for text, trans_start, trans_end in transcriptions:
                    # Adjust timestamps relative to the cropped video
                    if trans_start >= start and trans_end <= stop:
                        adjusted_start = trans_start - start
                        adjusted_end = trans_end - start
                        cropped_transcriptions.append((text, adjusted_start, adjusted_end))
                    elif trans_start < stop and trans_end > start:
                        # Partial overlap - adjust accordingly
                        adjusted_start = max(0, trans_start - start)
                        adjusted_end = min(stop - start, trans_end - start)
                        if adjusted_end > adjusted_start:
                            cropped_transcriptions.append((text, adjusted_start, adjusted_end))

                combine_videos("Out.mp4", croped, "Final.mp4", cropped_transcriptions, subtitle_style)
            else:
                print("Error in getting highlight")
        else:
            print("No transcriptions found")
    else:
        print("No audio file found")
else:
    print("Unable to Download the video")