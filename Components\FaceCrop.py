import cv2
import numpy as np
import os
from moviepy.editor import *
from Components.Speaker import detect_faces_and_speakers, Frames
from Components.SubtitleGenerator import overlay_subtitles_on_video, create_output_folder, generate_output_filename
global Fps

# Add FFmpeg to PATH
ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                          "ffmpeg-master-latest-win64-gpl", "bin")
if os.path.exists(ffmpeg_path):
    os.environ["PATH"] += os.pathsep + ffmpeg_path
    print(f"Added FFmpeg to PATH in FaceCrop.py: {ffmpeg_path}")

def crop_to_vertical(input_video_path, output_video_path):
    detect_faces_and_speakers(input_video_path, "DecOut.mp4")

    # Also save intermediate files to output folder
    output_folder = create_output_folder()
    base_name = os.path.splitext(os.path.basename(output_video_path))[0]
    organized_output = os.path.join(output_folder, generate_output_filename(f"{base_name}_raw_cropped.mp4", ""))
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

    cap = cv2.VideoCapture(input_video_path, cv2.CAP_FFMPEG)
    if not cap.isOpened():
        print("Error: Could not open video.")
        return

    original_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    original_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    vertical_height = int(original_height)
    vertical_width = int(vertical_height * 9 / 16)
    print(vertical_height, vertical_width)


    if original_width < vertical_width:
        print("Error: Original video width is less than the desired vertical width.")
        return

    x_start = (original_width - vertical_width) // 2
    x_end = x_start + vertical_width
    print(f"start and end - {x_start} , {x_end}")
    print(x_end-x_start)
    half_width = vertical_width // 2

    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_video_path, fourcc, fps, (vertical_width, vertical_height))
    global Fps
    Fps = fps
    print(fps)
    count = 0
    for _ in range(total_frames):
        ret, frame = cap.read()
        if not ret:
            print("Error: Could not read frame.")
            break
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30))

        # Initialize default values
        x, y, w, h = 0, 0, vertical_width, original_height

        # Try to get the active speaker location from Frames
        active_speaker_found = False
        if len(Frames) > count:
            try:
                (speaker_x, speaker_y, speaker_w, speaker_h) = Frames[count]

                # If we have detected faces, find the one closest to the active speaker
                if len(faces) > 0:
                    best_face = None
                    min_distance = float('inf')

                    for face in faces:
                        face_x, face_y, face_w, face_h = face
                        face_center_x = face_x + face_w // 2
                        speaker_center_x = speaker_x + speaker_w // 2

                        # Calculate distance between face center and speaker center
                        distance = abs(face_center_x - speaker_center_x)

                        if distance < min_distance:
                            min_distance = distance
                            best_face = face

                    # Use the best matching face if found and close enough
                    if best_face is not None and min_distance < original_width // 4:
                        x, y, w, h = best_face
                        active_speaker_found = True
                        print(f"Using detected face: x={x}, w={w}")

                # If no good face match, use speaker data directly
                if not active_speaker_found:
                    x, y, w, h = speaker_x, speaker_y, speaker_w, speaker_h
                    active_speaker_found = True
                    print(f"Using speaker data: x={x}, w={w}")

            except Exception as e:
                print(f"Error accessing Frames[{count}]: {e}")

        # Fallback: use first detected face or center crop
        if not active_speaker_found:
            if len(faces) > 0:
                x, y, w, h = faces[0]
                print(f"Using first detected face: x={x}, w={w}")
            else:
                # Default center position
                x = (original_width - vertical_width) // 2
                y = 0
                w = vertical_width
                h = original_height
                print(f"Using center crop: x={x}, w={w}")

        # Calculate center of detected face
        centerX = x + (w // 2)

        # Calculate new crop window centered on face
        new_x_start = centerX - half_width
        new_x_end = centerX + half_width

        # Apply boundary constraints to prevent negative coordinates and black areas
        if new_x_start < 0:
            new_x_start = 0
            new_x_end = vertical_width
        elif new_x_end > original_width:
            new_x_end = original_width
            new_x_start = original_width - vertical_width

        # Smooth movement: only update if significant change (reduces jitter)
        movement_threshold = 10  # pixels
        if count == 0 or abs(new_x_start - x_start) > movement_threshold:
            x_start = new_x_start
            x_end = new_x_end

        # Ensure coordinates are within bounds
        x_start = max(0, min(x_start, original_width - vertical_width))
        x_end = x_start + vertical_width

        # Debug output
        print(f"Frame {count}: centerX={centerX}, x_start={x_start}, x_end={x_end}")

        count += 1

        # Crop the frame
        cropped_frame = frame[:, x_start:x_end]

        # Final safety check
        if cropped_frame.shape[1] != vertical_width or cropped_frame.shape[1] == 0:
            print(f"Warning: Invalid crop size {cropped_frame.shape}, using center crop")
            x_start = (original_width - vertical_width) // 2
            x_end = x_start + vertical_width
            cropped_frame = frame[:, x_start:x_end]

        print(cropped_frame.shape)

        out.write(cropped_frame)

    cap.release()
    out.release()
    print("Cropping complete. The video has been saved to", output_video_path, count)

    # Also save a copy to the organized output folder
    try:
        import shutil
        shutil.copy2(output_video_path, organized_output)
        print(f"✅ Raw cropped video also saved to: {organized_output}")
    except Exception as e:
        print(f"Warning: Could not save organized copy: {e}")



def combine_videos(video_with_audio, video_without_audio, output_filename, transcriptions=None, subtitle_style='modern'):
    try:
        # Create output folder
        output_folder = create_output_folder()

        # Generate organized filenames
        base_name = os.path.splitext(os.path.basename(output_filename))[0]

        # Create different output files in the output folder
        cropped_output = os.path.join(output_folder, generate_output_filename(f"{base_name}_cropped.mp4", ""))
        final_output_path = os.path.join(output_folder, generate_output_filename(f"{base_name}_final.mp4", ""))

        # Load video clips
        clip_with_audio = VideoFileClip(video_with_audio)
        clip_without_audio = VideoFileClip(video_without_audio)

        audio = clip_with_audio.audio
        combined_clip = clip_without_audio.set_audio(audio)

        global Fps

        # Save cropped video without subtitles first
        print(f"Saving cropped video to: {cropped_output}")
        combined_clip.write_videofile(cropped_output, codec='libx264', audio_codec='aac', fps=Fps, preset='medium', bitrate='3000k')

        # If transcriptions are provided, add subtitles
        if transcriptions:
            print("Adding subtitles to the final video...")

            # Close clips to free memory
            clip_with_audio.close()
            clip_without_audio.close()
            combined_clip.close()

            # Add subtitles to the combined video
            final_result = overlay_subtitles_on_video(cropped_output, transcriptions, final_output_path, subtitle_style)

            if final_result:
                print(f"✅ Final video with subtitles saved: {final_output_path}")
                print(f"✅ Cropped video (no subtitles) saved: {cropped_output}")

                # Also save to the original location for backward compatibility
                if final_result != output_filename:
                    import shutil
                    shutil.copy2(final_result, output_filename)
                    print(f"✅ Copy saved to: {output_filename}")
            else:
                print("❌ Error adding subtitles")
        else:
            # Close clips
            clip_with_audio.close()
            clip_without_audio.close()
            combined_clip.close()

            print(f"✅ Combined video saved: {cropped_output}")

            # Copy to original location for backward compatibility
            import shutil
            shutil.copy2(cropped_output, output_filename)
            print(f"✅ Copy saved to: {output_filename}")

    except Exception as e:
        print(f"❌ Error combining video and audio: {str(e)}")



if __name__ == "__main__":
    input_video_path = r'Out.mp4'
    output_video_path = 'Croped_output_video.mp4'
    final_video_path = 'final_video_with_audio.mp4'

    # Example transcriptions for testing
    test_transcriptions = [
        ("Hello everyone, welcome to this video", 0.0, 3.0),
        ("Today we're going to talk about AI", 3.0, 6.0),
        ("This is really exciting stuff", 6.0, 9.0)
    ]

    detect_faces_and_speakers(input_video_path, "DecOut.mp4")
    crop_to_vertical(input_video_path, output_video_path)
    combine_videos(input_video_path, output_video_path, final_video_path, test_transcriptions)



