import os
import sys
from pytubefix import YouTube
import ffmpeg
import subprocess

# Add FFmpeg to PATH
ffmpeg_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                          "ffmpeg-master-latest-win64-gpl", "bin")
if os.path.exists(ffmpeg_path):
    os.environ["PATH"] += os.pathsep + ffmpeg_path
    print(f"Added FFmpeg to PATH: {ffmpeg_path}")

def get_video_size(stream):

    return stream.filesize / (1024 * 1024)

def download_youtube_video(url):
    try:
        yt = YouTube(url)

        video_streams = yt.streams.filter(type="video").order_by('resolution').desc()
        audio_stream = yt.streams.filter(only_audio=True).first()

        print("Available video streams:")
        for i, stream in enumerate(video_streams):
            size = get_video_size(stream)
            stream_type = "Progressive" if stream.is_progressive else "Adaptive"
            print(f"{i}. Resolution: {stream.resolution}, Size: {size:.2f} MB, Type: {stream_type}")

        choice = int(input("Enter the number of the video stream to download: "))
        selected_stream = video_streams[choice]

        if not os.path.exists('videos'):
            os.makedirs('videos')

        print(f"Downloading video: {yt.title}")
        video_file = selected_stream.download(output_path='videos', filename_prefix="video_")

        if not selected_stream.is_progressive:
            print("Downloading audio...")
            audio_file = audio_stream.download(output_path='videos', filename_prefix="audio_")

            print("Merging video and audio...")
            output_file = os.path.join('videos', f"{yt.title}.mp4")

            try:
                # Try using ffmpeg Python package first
                stream = ffmpeg.input(video_file)
                audio = ffmpeg.input(audio_file)
                stream = ffmpeg.output(stream, audio, output_file, vcodec='libx264', acodec='aac', strict='experimental')
                ffmpeg.run(stream, overwrite_output=True)
            except Exception as e:
                print(f"Error with ffmpeg Python package: {e}")
                print("Trying direct FFmpeg command...")

                # Fallback to direct FFmpeg command
                try:
                    cmd = ['ffmpeg', '-i', video_file, '-i', audio_file, '-c:v', 'libx264',
                           '-c:a', 'aac', '-strict', 'experimental', output_file, '-y']
                    subprocess.run(cmd, check=True)
                    print("FFmpeg direct command successful")
                except subprocess.CalledProcessError as e:
                    print(f"FFmpeg command failed: {e}")
                    raise

            os.remove(video_file)
            os.remove(audio_file)
        else:
            output_file = video_file


        print(f"Downloaded: {yt.title} to 'videos' folder")
        print(f"File path: {output_file}")
        return output_file

    except Exception as e:
        print(f"An error occurred: {str(e)}")
        print("Please make sure you have the latest version of pytube and ffmpeg-python installed.")
        print("You can update them by running:")
        print("pip install --upgrade pytube ffmpeg-python")
        print("Also, ensure that ffmpeg is installed on your system and available in your PATH.")

if __name__ == "__main__":
    youtube_url = input("Enter YouTube video URL: ")
    download_youtube_video(youtube_url)
